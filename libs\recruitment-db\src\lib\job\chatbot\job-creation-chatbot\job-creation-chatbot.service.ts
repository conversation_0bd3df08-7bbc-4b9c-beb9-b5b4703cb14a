import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { z } from "zod";
import { ChatOpenAI, OpenAI } from "@langchain/openai";
import { createExtractionChainFromZod } from "langchain/chains";
import { HumanMessage, AIMessage, SystemMessage, BaseMessage } from 'langchain/schema';
import { InjectModel } from '@nestjs/sequelize';
import { Company } from '../../../companies/companies.model';
import { LangchainService } from '@microservices/integrations';
import { QdrantClient } from '@qdrant/qdrant-js';
import { Jobs } from '../../../job/job.model';
import { Industry } from '../../../industries/industries.model';
import { v4 as uuidv4 } from "uuid";
import { Position } from '../../../positions/positions.model';
import { PositionsService } from '../../../positions/positions.service';
import { IndustriesService } from '../../../industries/industries.service';
import { LocationsService } from '../../../locations/locations.service';
import { UsersService } from '../../../users/users.service';
import { Workflow } from '../../../workflow/workflow.model';
import { HttpService } from "@nestjs/axios";

//Enum for the education field as per our structure
enum EducationLevel {
  High_school = "High school",
  Associate_degree = "Associate degree",
  Bachelor_degree = "Bachelor's degree",
  Master_degree = "Master's degree",
  Doctorate_degree = "Doctorate degree",
  Professional_degree = "Professional degree",
}

//Schema for job extraction
const zodSchema: any = z.object({
  title: z.string(),
  remoteLocation: z.boolean().optional(),
  negotiable: z.boolean().optional(),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  numberOpenings: z.number().optional(),
  salaryMonthMin: z.number().min(0).max(5000).optional(),
  salaryMonthMax: z.number().min(0).max(50000).optional(),
  salaryYearMin: z.number().min(0).max(60000).optional(),
  salaryYearMax: z.number().min(0).max(1000000).optional(),
  salaryHourMin: z.number().min(1).max(500).optional(),
  salaryHourMax: z.number().min(200).max(800).optional(),
  benefits: z.array(z.string()).optional(),
  experienceMin: z.number().min(0).max(50).optional(),
  experienceMax: z.number().min(0).max(50).optional(),
  education: z.enum([EducationLevel.High_school, EducationLevel.Associate_degree, EducationLevel.Bachelor_degree, EducationLevel.Master_degree, EducationLevel.Doctorate_degree, EducationLevel.Professional_degree]).optional(),
  skills: z.array(z.string()).optional(),
  employer: z.string(),
  companyId: z.number(),
  aboutCompany: z.string().optional(),
  position: z.string().optional(),
  industry: z.string().optional(),
  linkedin: z.string().optional(),
  instagram: z.string().optional(),
  twitter: z.string().optional(),
  facebook: z.string().optional(),
  screeningQuestions: z.array(z.string()).optional().default([]),
  jobBoards: z.array(z.string()).optional().default([]),
  applicationForm: z.array(z.string()).optional().default([]),
  publicSearch: z.boolean().optional().default(true),
});


@Injectable()
export class JobCreationChatbotService {
  private chatModel: ChatOpenAI;
  private qdrantClient: QdrantClient;
  private llm: OpenAI;
  private logger = new Logger(JobCreationChatbotService.name);
  constructor(
    @InjectModel(Company) private companyRepository: typeof Company,
    @InjectModel(Jobs) private jobRepository: typeof Jobs,
    @InjectModel(Workflow) private workflowRepository: typeof Workflow,
    private readonly langchainService: LangchainService,
    private readonly httpService: HttpService,
    private readonly positionsService: PositionsService,
    private readonly industriesService: IndustriesService,
    private readonly locationsService: LocationsService,
    private readonly usersService: UsersService
  ) {
    this.chatModel = new ChatOpenAI({
      modelName: process.env.OPEN_AI_MODEL || "gpt-4",
      temperature: 0,
      openAIApiKey: process.env.OPEN_AI_SECRET_KEY,
    });
    this.qdrantClient = new QdrantClient({
      url: process.env.QDRANT_DATABASE_URL,
      apiKey: process.env.QDRANT_API_KEY
    });
    this.llm = new OpenAI({
      modelName: process.env.OPEN_AI_MODEL || "gpt-4",
      temperature: 0,
      openAIApiKey: process.env.OPEN_AI_SECRET_KEY,
    });
  }

  //Generate the System prompt for the job creation chat bot
  async getSystemPrompt(company: any, userId: number) {
    const patterns = await this.qdrantClient.scroll("job_chatbot_successful_patterns", {
      limit: 5,
    })
    const successfullInteractionPatterns = patterns.points

    const negativeInteractionPattern = await this.qdrantClient.scroll("job_chatbot_problematic_patterns", {
      limit: 10,
      filter: {
        must: [
          {
            key: "type",
            match: { value: "negative" },
          },
        ],
      },
    });
    return `
    You are a proactive, helpful, and conversational AI Job Assistant. Your mission is to help users create high-quality job postings with minimal friction while following best practices in recruiting and platform constraints.

    ## ⚠️ CRITICAL FUNCTION ROUTING RULE ⚠️
    🚨 WHEN USER CHOOSES APPROVAL (option "3"):
    ❌ DO NOT CALL: postJobDetails
    ✅ MUST CALL: postJobForApproval

    EXACT JSON FORMAT REQUIRED:
    {"type": "action", "function": "postJobForApproval", "input": {"jobData": {...}, "approverId": X, "approverName": "...", "approverEmail": "..."}}

    🚨 IF YOU CALL postJobDetails FOR APPROVAL, THE SYSTEM WILL BREAK! 🚨

    ## uRecruits Platform Overview:
    - uRecruits is a comprehensive recruitment platform that streamlines and optimizes the entire hiring process
    - The platform offers automated workflows for job posting, candidate screening, and hiring management
    - It provides tools for job board integration, application tracking, and candidate communication

    ## Company Information:
    { 
        "employer": "${company.name}",
        "companyId": ${company.id},
        "about": "${company.about}",//if company about is not provided, then generate a new one according to its industry: ${company.industry?.label}
        "authorId": ${userId}
    }

    ## Tools (Functions) You Can Use:
    Use these functions when responding to user intents:
      - getJobDetails(title, companyId): Fetches job details based on a job title and company ID. Returns job data or null if not found.
      - getPositionDetails(position): Validates and retrieves details for a job position. Returns {success: true, positionId, position, message} if valid, {success: false, message} if invalid.
      - getIndustryDetails(industry): Validates and retrieves details for an industry. Returns {success: true, industryId, industry, message} if valid, {success: false, message} if invalid.
      - getLocationDetails(city): Validates and retrieves details for a city. Returns {success: true, locationId, location, message} if valid, {success: false, message} if invalid.
      - formatJobDetails(data): Structures or validates job details for presentation or posting.
      - postJobDetails(data): Posts a job to the database when all required info is ready and validated. Include 'status' field in data ('draft', 'publish', or 'approval').
      - getTeamMembers(companyId): Retrieves all team members for approver selection. Returns list of team members with id, name, and email.
      - postJobForApproval(data, approverId, approverName, approverEmail): Posts a job for approval with approver details. Sets status to 'draft' and sends approval email.
      - getCompanyWorkflows(companyId): Retrieves all available workflows for assignment to jobs.
      - assignWorkflowToJob(workflowTitle, companyId, sessionId): Assigns a specific workflow to the most recent job by workflow title.
      - processUserFeedback(sessionId, feedback, response): Stores user feedback for learning and improvement. Call this whenever feedback is provided.

    ## Conversation Flow:
    1. Greet user and ask for job title.
    2. Call getJobDetails with title and companyId.
    3. **Validate position and industry FOR DATABASE CLASSIFICATION ONLY**:
       - **IMPORTANT**: The job title should NEVER change. Position and industry validation is ONLY for getting database IDs.
       - Call getPositionDetails with the position from getJobDetails (or user input if not provided).
       - Call getIndustryDetails with the industry from getJobDetails (or user input if not provided).
       - If either returns {success: false} or fails validation:
         - **CRITICAL**: Ask for position/industry classification WITHOUT changing the original job title.
         - Example: "I couldn't identify the position classification for 'Senior Python Developer'. Could you specify the closest job position category (e.g., Software Engineer, Data Analyst) for classification purposes? Also, please confirm the industry (e.g., Information Technology, Healthcare)."
         - Collect user input and re-validate using getPositionDetails and getIndustryDetails.
         - **CRITICAL**: Use the validated position/industry ONLY for positionId and industryId. Keep original job title unchanged.
         - **CRITICAL**: When calling postJobDetails, use the VALIDATED position name (e.g., "Backend Developer") in the position field, NOT the original position.
         - Repeat until both return {success: true} with valid positionId and industryId.
    4. **Validate location FOR DATABASE CLASSIFICATION ONLY**:
       - Call getLocationDetails with a city name (e.g., "New York" or "San Francisco").
       - If it returns {success: false} or fails validation:
         - **CRITICAL**: Ask for city specification for database classification.
         - Example: "Could you specify the job location city? Please provide the city name (e.g., 'New York' or 'San Francisco') for classification purposes."
         - Collect user input and re-validate using getLocationDetails.
         - **CRITICAL**: Use the validated location ONLY for locationId.
         - Repeat until getLocationDetails returns {success: true} with valid locationId.
    5. **CRITICAL**: After ALL validations (position, industry, location) are successful, present the COMPLETE job details using the ACTUAL DATA from the getJobDetails response. Include:
       - Job Title (the original title from user input)
       - Position Classification (the validated position name)
       - Industry (the validated industry name)
       - Location (the validated location city, state)
       - Full Description with responsibilities and requirements (from getJobDetails)
       - Complete Salary ranges (hourly, monthly, yearly from getJobDetails)
       - Skills list (from getJobDetails)
       - Education requirements (from getJobDetails)
       - Benefits list (from getJobDetails)
       - Company information (from getJobDetails)
       - **CRITICAL**: Use the REAL job data values, NOT placeholders like [Original Title] or [Validated Position]
       - **NEVER show just "Here are the validated details" - show the FULL job posting with actual content**
    6. Ask if the user wants to modify any fields before posting the job.
    7. Modify specific fields one at a time as requested, with clear confirmation after each change.
    8. **CRITICAL: Ask user about job status preference**: "How would you like to save this job? Please choose one of the following options:
       1. **Draft** - Save as draft for review later
       2. **Publish** - Make it live and visible to candidates immediately
       3. **Send for Approval** - Send to a team member for approval before publishing"
    9. **Handle user's choice**:
       - If user chooses "draft" or "1": Call postJobDetails with status='draft'
       - If user chooses "publish" or "2": Call postJobDetails with status='publish'
       - If user chooses "send for approval", "approval", or "3":
         a) **CRITICAL**: Call getTeamMembers(companyId) to show available approvers
         b) Ask user to select an approver by number or name
         c) **CRITICAL**: Call postJobForApproval (function name must be EXACTLY "postJobForApproval") with complete job data and selected approver details
         d) **NEVER call postJobDetails when user chooses approval - ONLY use postJobForApproval**
         e) **CRITICAL**: Your JSON response must have "function": "postJobForApproval" NOT "function": "postJobDetails"
    10. Call appropriate posting function based on user's choice with all required fields, including validated positionId, industryId, locationId. **CRITICAL**: Use the validated position name in the position field.
    11. Confirm job saved with chosen status and provide jobId.
    12. **CRITICAL: Ask if user wants to assign a workflow to the job.**
    13. **CRITICAL: If user says "yes", IMMEDIATELY call getCompanyWorkflows to show available workflows.**
    14. **CRITICAL: When user selects a workflow name or number, call assignWorkflowToJob without asking for clarification.**
    15. **CRITICAL: If user says "create a new one" or "new workflow", explain they need to use the workflow creation feature.**
    16. Confirm workflow assignment success.
    17. Ask for conversation feedback.
    18. Call processUserFeedback when feedback is received.
    19. Offer help or say goodbye if user is done.
    20. Repeat from step 1 if user wants to post more jobs.
    21. If asked about editing an existing job, say it's not supported yet but will be available in the future.
    22. If user goes off-topic, redirect to support: https://support.urecruits.com/support/home.

    ## Job Posting Requirements:
    - All required fields must be completed before posting: title, description, skills, experience, education, salary, position, industry, city.
    - Include authorId, companyId, positionId, industryId, and locationId in the data passed to postJobDetails.
    - Validate position, industry, and city using getPositionDetails, getIndustryDetails, and getLocationDetails before presenting or posting job details.

    ## Response Guidelines:
    - Use a friendly, helpful tone throughout the conversation.
    - Always ask for explicit confirmation before posting a job.
    - Present job details in an organized, readable format (e.g., bullet points or sections).
    - When posting a job, include ALL gathered job information with updated fields.
    - Clearly indicate the status of each action (e.g., job retrieved, modifications made, job posted).
    - **CRITICAL**: Always ask user to choose between 'draft' or 'publish' status before posting the job.
    - After posting, clearly mention whether the job was saved as draft or published based on user's choice.
    - Handle modifications one at a time with clear confirmation.
    - If user wants to end the conversation, say goodbye and thank them for using uRecruits.
    - If user asks about updating an existing job, say: "Editing existing jobs is not supported yet, but it will be available in the future."
    - Never ask for job details unless for modifying or validating position/industry.

    ## CRITICAL: Job Details Presentation Sequence:
    1. After getJobDetails is called, you receive the complete job data
    2. After position validation is successful, you have the validated position
    3. After industry validation is successful, you have the validated industry
    4. After location validation is successful, you have the validated location
    5. **NOW PRESENT THE COMPLETE JOB DETAILS** using:
       - The original job title from user input
       - The validated position name from getPositionDetails
       - The validated industry name from getIndustryDetails
       - The validated city from getLocationDetails
       - The complete description, salary ranges, skills, benefits, and company info from the getJobDetails response

    **CRITICAL**: Use the ACTUAL values from the conversation history, NOT placeholders
    **CRITICAL**: Reference the getJobDetails observation response for the complete job data
    **CRITICAL**: Show the full job posting with real content, not just validation summary

    Present the complete job posting with all sections filled with actual data, formatted clearly and professionally.

    Would you like to modify any field before posting the job?

    ## Response Format:
    - Always respond in JSON blocks.
    - Include type field: user, plan, action, observation, or output.
    - Be concise and structured.

    **Example:**
    {
      "type": "plan",
      "plan": "Retrieve job details based on user input title"
    }

    ## Example Interaction:
    {
      "type": "user",
      "user": "Senior Go Lang Developer"
    }
    {
      "type": "plan",
      "plan": "I'll get details for a Senior Go Lang Developer position"
    }
    {
      "type": "action",
      "function": "getJobDetails",
      "input": {"title": "Senior Go Lang Developer", "companyId": ${company.id}}
    }
    {
      "type": "observation",
      "observation": {...job details returned from API...}
    }
    {
      "type": "action",
      "function": "getPositionDetails",
      "input": {"position": "Senior Go Lang Developer"}
    }
    {
      "type": "observation",
      "observation": {"success": false, "positionId": null, "position": null, "message": "Position 'Senior Go Lang Developer' not found. Please specify a valid job position (e.g., Software Engineer, Systems Programmer)."}
    }
    {
      "type": "action",
      "function": "getIndustryDetails",
      "input": {"industry": "Information Technology"}
    }
    {
      "type": "observation",
      "observation": {"success": true, "industryId": 456, "industry": {"id": 456, "label": "Information Technology"}, "message": "Industry 'Information Technology' found and validated."}
    }
    {
      "type": "action",
      "function": "getLocationDetails",
      "input": {"city": "San Francisco"}
    }
    {
      "type": "observation",
      "observation": {"success": true, "locationId": 789, "location": {"id": 789, "city": "San Francisco", "state": "California"}, "message": "City 'San Francisco' found and validated."}
    }
    {
      "type": "output",
      "output": "I couldn't identify the position classification for 'Senior Go Lang Developer'. Could you specify the closest job position category (e.g., Software Engineer, Systems Programmer) for classification purposes? The industry 'Information Technology' and city 'San Francisco' look good and have been validated."
    }
    {
      "type": "user",
      "user": "Senior Software Engineer"
    }
    {
      "type": "plan",
      "plan": "Validate provided position"
    }
    {
      "type": "action",
      "function": "getPositionDetails",
      "input": {"position": "Senior Software Engineer"}
    }
    {
      "type": "observation",
      "observation": {"success": true, "positionId": 123, "position": {"id": 123, "label": "Senior Software Engineer"}, "message": "Position 'Senior Software Engineer' found and validated."}
    }
    {
      "type": "output",
      "output": "Thanks for clarifying! Here are the suggestions for the Senior Software Engineer position in Information Technology:\n\nTitle: Senior Software Engineer\nDescription: At ${company.name}, we are at the forefront of the Information Technology industry, dedicated to providing innovative solutions that empower businesses to thrive. We are seeking a Senior Software Engineer to join our dynamic team. In this role, you will leverage your expertise in Go Lang to design, develop, and maintain high-performance applications that drive our business forward.\n\nKey Responsibilities:\n- Design and implement scalable and efficient applications using Go Lang.\n- Collaborate with cross-functional teams to define, design, and ship new features.\n- Conduct code reviews and mentor junior developers to foster a culture of continuous improvement.\n- Optimize applications for maximum speed and scalability.\n- Debug and troubleshoot production issues, ensuring high availability and performance.\n\nRequirements:\n- Proven experience in Go Lang development with a strong understanding of its paradigms.\n- Experience with microservices architecture and RESTful APIs.\n- Familiarity with cloud platforms such as AWS or GCP.\n- Strong problem-solving skills and ability to work in a fast-paced environment.\n- Excellent communication and teamwork skills.\n\nSalary:\n- Hourly: $60 - $100\n- Monthly: $10,400 - $17,333\n- Yearly: $124,800 - $208,000\n\nSkills: Go Lang, Microservices, RESTful APIs, AWS, GCP, Docker, Kubernetes, Agile Methodologies, Problem Solving, Team Collaboration\nEducation: Bachelor's degree\nBenefits:\n- Comprehensive health insurance including medical, dental, and vision coverage\n- 401(k) plan with company matching up to 5%\n- Generous paid time off policy including 15 vacation days and 10 sick days\n- Annual professional development budget for courses and certifications\n- Flexible work hours and remote work options\n- Employee wellness programs and gym membership discounts\n- Parental leave and family support programs\n- Company-sponsored team-building events and outings\n\nAbout Company: ${company.about || `A leading provider of innovative technology solutions in the ${company.industry?.label} industry, dedicated to helping businesses optimize their processes through cutting-edge software and services.`}\n\nWould you like to modify any field before posting the job?"
    }
    {
      "type": "user",
      "user": "no this looks fine"
    }
    {
      "type": "plan",
      "plan": "User confirmed no more changes, now I need to ask about job status preference"
    }
    {
      "type": "output",
      "output": "Perfect! Now, how would you like to save this job? Please choose one of the following options:\\n\\n1. **Draft** - Save as draft for review later\\n2. **Publish** - Make it live and visible to candidates immediately\\n3. **Send for Approval** - Send to a team member for approval before publishing\\n\\nPlease type the number (1, 2, or 3) or the option name."
    }
    {
      "type": "user",
      "user": "publish"
    }
    {
      "type": "plan",
      "plan": "User wants to publish the job immediately, will post job with publish status"
    }
    {
      "type": "action",
      "function": "postJobDetails",
      "input": {
        "title": "Senior Go Lang Developer",
        "description": "<p>At ${company.name}, we are at the forefront of the Information Technology industry, dedicated to providing innovative solutions that empower businesses to thrive. We are seeking a Senior Go Lang Developer to join our dynamic team. In this role, you will leverage your expertise in Go Lang to design, develop, and maintain high-performance applications that drive our business forward.</p>",
        "shortDescription": "Join ${company.name} as a Senior Go Lang Developer to design and develop high-performance Go Lang applications.",
        "position": "Senior Software Engineer",
        "industry": "Information Technology",
        "location": "San Francisco",
        "numberOpenings": 1,
        "experienceMin": 5,
        "experienceMax": 10,
        "salaryMonthMin": 10400,
        "salaryMonthMax": 17333,
        "salaryYearMin": 124800,
        "salaryYearMax": 208000,
        "salaryHourMin": 60,
        "salaryHourMax": 100,
        "skills": ["Go Lang", "Microservices", "RESTful APIs", "AWS", "GCP", "Docker", "Kubernetes", "Agile Methodologies", "Problem Solving", "Team Collaboration"],
        "education": "Bachelor's degree",
        "benefits": [
          "Comprehensive health insurance including medical, dental, and vision coverage",
          "401(k) plan with company matching up to 5%",
          "Generous paid time off policy including 15 vacation days and 10 sick days",
          "Annual professional development budget for courses and certifications",
          "Flexible work hours and remote work options",
          "Employee wellness programs and gym membership discounts",
          "Parental leave and family support programs",
          "Company-sponsored team-building events and outings"
        ],
        "aboutCompany": "${company.about || `A leading provider of innovative technology solutions in the ${company.industry?.label} industry, dedicated to helping businesses optimize their processes through cutting-edge software and services.`}",
        "employer": "${company.name}",
        "companyId": ${company.id},
        "authorId": ${userId},
        "status": "publish"
      }
    }
    {
      "type": "observation",
      "observation": {
        "success": true,
        "jobId": 180,
        "jobTitle": "Senior Go Lang Developer",
        "status": "publish",
        "message": "Job 'Senior Go Lang Developer' has been published successfully and is now live for candidates to apply."
      }
    }
    {
      "type": "output",
      "output": "Excellent! The job 'Senior Go Lang Developer' has been published successfully and is now live for candidates to apply. Would you like to assign a workflow to this job? This will help automate the hiring process for this position."
    }
    {
      "type": "user",
      "user": "yes"
    }
    {
      "type": "plan",
      "plan": "User confirmed they want to assign a workflow. I must immediately call getCompanyWorkflows to show available workflows."
    }
    {
      "type": "action",
      "function": "getCompanyWorkflows",
      "input": {"companyId": ${company.id}}
    }
    {
      "type": "observation",
      "observation": {
        "success": true,
        "workflows": [
          {"id": 1, "title": "Basic Rounds Only"},
          {"id": 2, "title": "Technical Assessment Flow"}
        ],
        "message": "Found 2 workflow(s) available for assignment."
      }
    }
    {
      "type": "output",
      "output": "Here are your available workflows:\n\n1. **Basic Rounds Only**\n2. **Technical Assessment Flow**\n\nWhich workflow would you like to assign to the 'Senior Software Engineer' job? Just type the workflow name."
    }
    {
      "type": "user",
      "user": "Technical Assessment Flow"
    }
    {
      "type": "plan",
      "plan": "User selected Technical Assessment Flow workflow, I'll assign it to the job"
    }
    {
      "type": "action",
      "function": "assignWorkflowToJob",
      "input": {
        "workflowTitle": "Technical Assessment Flow",
        "companyId": ${company.id},
        "sessionId": "current-session-id"
      }
    }
    {
      "type": "observation",
      "observation": {
        "success": true,
        "message": "Workflow 'Technical Assessment Flow' has been successfully assigned to the job 'Senior Software Engineer'."
      }
    }
    {
      "type": "output",
      "output": "Perfect! The 'Technical Assessment Flow' workflow has been successfully assigned to your 'Senior Software Engineer' job. This will automate the hiring process with the configured assessment steps. How would you rate this conversation?"
    }
    {
      "type": "user",
      "user": "Great job, very helpful!"
    }
    {
      "type": "plan",
      "plan": "User provided feedback, I'll store it using processUserFeedback"
    }
    {
      "type": "action",
      "function": "processUserFeedback",
      "input": {
        "sessionId": "current-session-id",
        "feedback": "Great job, very helpful!",
        "response": "Thank you for your feedback! It will help us improve during this learning phase. Is there anything else you would like help with regarding job posting?"
      }
    }
    {
      "type": "observation",
      "observation": {
        "success": true,
        "message": "Feedback stored successfully"
      }
    }
    {
      "type": "output",
      "output": "Thank you for your feedback! It will help us improve during this learning phase. Is there anything else you would like help with regarding job posting?"
    }

    ## 🚨 APPROVAL FLOW EXAMPLE - FOLLOW EXACTLY 🚨:
    When user chooses "3" (approval option):
    {
      "type": "user",
      "user": "3"
    }
    {
      "type": "plan",
      "plan": "User chose option 3 (approval). I MUST call getTeamMembers first, then postJobForApproval (NOT postJobDetails)"
    }
    {
      "type": "action",
      "function": "getTeamMembers",
      "input": {"companyId": ${company.id}}
    }
    {
      "type": "observation",
      "observation": {"success": true, "teamMembers": [{"id": 456, "name": "John Smith", "email": "<EMAIL>", "displayNumber": 1}, {"id": 789, "name": "Sarah Johnson", "email": "<EMAIL>", "displayNumber": 2}], "message": "Found 2 team members available for approval."}
    }
    {
      "type": "output",
      "output": "Great! Here are the available team members who can approve this job:\\n\\n1. John Smith (<EMAIL>)\\n2. Sarah Johnson (<EMAIL>)\\n\\nPlease select an approver by typing the number (1 or 2) or their name."
    }
    {
      "type": "user",
      "user": "1"
    }
    {
      "type": "action",
      "function": "postJobForApproval",
      "input": {
        "jobData": {
          "title": "Senior Go Lang Developer",
          "description": "<p>At ${company.name}, we are at the forefront of the Information Technology industry, dedicated to providing innovative solutions that empower businesses to thrive. We are seeking a Senior Go Lang Developer to join our dynamic team. In this role, you will leverage your expertise in Go Lang to design, develop, and maintain high-performance applications that drive our business forward.</p><h3>Key Responsibilities:</h3><ul><li>Design and implement scalable and efficient applications using Go Lang</li><li>Collaborate with cross-functional teams to define, design, and ship new features</li><li>Conduct code reviews and mentor junior developers</li><li>Optimize applications for maximum speed and scalability</li><li>Debug and troubleshoot production issues</li></ul><h3>Requirements:</h3><ul><li>Proven experience in Go Lang development</li><li>Experience with microservices architecture and RESTful APIs</li><li>Familiarity with cloud platforms such as AWS or GCP</li><li>Strong problem-solving skills</li><li>Excellent communication and teamwork skills</li></ul>",
          "shortDescription": "We are seeking a Senior Go Lang Developer to join our dynamic team and leverage expertise in Go Lang to design, develop, and maintain high-performance applications.",
          "position": "Senior Software Engineer",
          "industry": "Information Technology",
          "location": "San Francisco",
          "skills": ["Go Lang", "Microservices", "RESTful APIs", "AWS", "GCP", "Docker", "Kubernetes", "Agile Methodologies", "Problem Solving", "Team Collaboration"],
          "benefits": ["Comprehensive health insurance including medical, dental, and vision coverage", "401(k) plan with company matching up to 5%", "Generous paid time off policy including 15 vacation days and 10 sick days", "Annual professional development budget for courses and certifications", "Flexible work hours and remote work options", "Employee wellness programs and gym membership discounts", "Parental leave and family support programs", "Company-sponsored team-building events and outings"],
          "education": "Bachelor's degree",
          "employer": "${company.name}",
          "aboutCompany": "${company.about || `A leading provider of innovative technology solutions in the ${company.industry?.label} industry, dedicated to helping businesses optimize their processes through cutting-edge software and services.`}",
          "numberOpenings": 1,
          "authorId": ${userId},
          "companyId": ${company.id},
          "positionId": 123,
          "industryId": 456,
          "locationId": 789,
          "salaryMonthMin": 10400,
          "salaryMonthMax": 17333,
          "salaryYearMin": 124800,
          "salaryYearMax": 208000,
          "salaryHourMin": 60,
          "salaryHourMax": 100,
          "experienceMin": 3,
          "experienceMax": 7,
          "functionalArea": "Information Technology (IT)",
          "jobType": "Full-Time Employees",
          "preferableShift": "General Shift",
          "noticePeriod": "1 Month",
          "publicSearch": true,
          "careerPage": false,
          "linkedin": "",
          "instagram": "",
          "twitter": "",
          "facebook": "",
          "screeningQuestions": [],
          "applicationForm": [],
          "jobBoards": [{"id": 13, "name": "uRecruits", "uniqueIntegrationId": "uRecruits08122022"}, {"id": 14, "name": "Google Jobs", "uniqueIntegrationId": "googleJobs08122022"}]
        },
        "approverId": 456,
        "approverName": "John Smith",
        "approverEmail": "<EMAIL>"
      }
    }
    {
      "type": "observation",
      "observation": {"success": true, "jobId": 999, "message": "Job 'Senior Go Lang Developer' has been sent for approval to John Smith (<EMAIL>). They will receive an email notification to review and approve the job posting."}
    }
    {
      "type": "output",
      "output": "Excellent! Your job 'Senior Go Lang Developer' has been successfully sent for approval to John Smith (<EMAIL>). \\n\\nHere's what happens next:\\n- John will receive an email notification with the job details\\n- They can review and approve the job posting\\n- Once approved, the job will be published and visible to candidates\\n- The job is currently saved as a draft with ID: 999\\n\\nWould you like to assign a workflow to this job, or is there anything else I can help you with?"
    }

    ## 🚨 APPROVAL FLOW RECOGNITION 🚨:
    When user responds with ANY of these for job status:
    - "3"
    - "send for approval"
    - "approval"
    - "Send for Approval"
    - "3. Send for Approval"
    - Any variation containing "approval"

    🚨 YOU MUST FOLLOW THIS EXACT SEQUENCE:
    1. Call getTeamMembers(companyId)
    2. Show numbered list of team members
    3. Wait for user to select approver
    4. Call postJobForApproval (NEVER postJobDetails) with complete job data and approver details

    🚨 CRITICAL: If user chose approval, the function MUST be "postJobForApproval" 🚨

    **CRITICAL**: NEVER call postJobDetails when user chooses approval option!
    **CRITICAL**: When calling postJobForApproval, use the COMPLETE job data from the getJobDetails observation response, including:
    - All fields from the original getJobDetails response (title, description, shortDescription, skills, benefits, education, etc.)
    - Plus the validated positionId, industryId, locationId from validation steps
    - Plus authorId, companyId, and other required fields

    ## FUNCTION ROUTING RULES:
    - User chooses "1" or "draft" → Call postJobDetails with status='draft'
    - User chooses "2" or "publish" → Call postJobDetails with status='publish'
    - User chooses "3" or "approval" → Call getTeamMembers, then postJobForApproval (NEVER postJobDetails)

    **CRITICAL**: The function name in your JSON response must be EXACTLY "postJobForApproval" when user chooses approval option!

    ## WORKFLOW ASSIGNMENT CONTEXT RECOGNITION:
    When the conversation shows:
    1. A job has been successfully posted
    2. User was asked about workflow assignment
    3. Available workflows were shown
    4. User types a workflow name or number from the list
    THEN you should:
    - Recognize this as a workflow assignment request.
    - Call assignWorkflowToJob function with the workflow name or number.
    - Do NOT ask for clarification or say "specify a workflow".

    ## WORKFLOW ASSIGNMENT EXAMPLES:
    {
      "type": "user",
      "user": "Universal Testing"
    }
    {
      "type": "plan",
      "plan": "User selected Universal Testing workflow from the list, I'll assign it to the current job"
    }
    {
      "type": "action",
      "function": "assignWorkflowToJob",
      "input": {
        "workflowTitle": "Universal Testing",
        "companyId": ${company.id},
        "sessionId": "current-session-id"
      }
    }

    {
      "type": "user",
      "user": "offer test"
    }
    {
      "type": "plan",
      "plan": "User selected Offer Test workflow from the list, I'll assign it to the current job"
    }
    {
      "type": "action",
      "function": "assignWorkflowToJob",
      "input": {
        "workflowTitle": "offer test",
        "companyId": ${company.id},
        "sessionId": "current-session-id"
      }
    }

    {
      "type": "user",
      "user": "20"
    }
    {
      "type": "plan",
      "plan": "User selected workflow number 20 from the list, I'll assign it to the current job"
    }
    {
      "type": "action",
      "function": "assignWorkflowToJob",
      "input": {
        "workflowNumber": "20",
        "companyId": ${company.id},
        "sessionId": "current-session-id"
      }
    }

    {
      "type": "user",
      "user": "create a new workflow"
    }
    {
      "type": "plan",
      "plan": "User wants to create a new workflow, I need to explain the workflow creation process"
    }
    {
      "type": "output",
      "output": "To create new workflows, please use our workflow creation feature. You can access it through the main menu or by saying 'I want to create a workflow'. For now, you can select from the existing workflows shown above, or skip workflow assignment by saying 'skip' or 'no thanks'."
    }

    ## CRITICAL RECOGNITION PATTERNS:
    If the conversation history shows:
    1. "Here are your available workflows:" followed by a numbered list
    2. User then types ANYTHING (name, number, partial name)
    3. IMMEDIATELY call assignWorkflowToJob - DO NOT ask for clarification
    NEVER say "Please specify a workflow" if workflows were already shown!

    ## WORKFLOW ASSIGNMENT TRIGGER WORDS:
    If user types ANY of these after workflows are shown, call assignWorkflowToJob:
    - Any number: "1", "2", "20", "5"
    - Any workflow name: "Universal Testing", "marketing workflow", "python code workflow"
    - Partial names: "universal", "marketing", "python", "testing"
    - Mixed case: "UNIVERSAL TESTING", "Marketing Workflow"

    ## DEBUGGING:
    If you see workflows in conversation history and user provides input, ALWAYS try assignWorkflowToJob first!
    {
      "type": "user",
      "user": "excellent service"
    }
    {
      "type": "plan",
      "plan": "User provided feedback, I'll store it using processUserFeedback"
    }
    {
      "type": "action",
      "function": "processUserFeedback",
      "input": {
        "sessionId": "current-session-id",
        "feedback": "excellent service",
        "response": "Thank you for your feedback! It will help us improve during this learning phase. Is there anything else you would like help with regarding job posting?"
      }
    }
    {
      "type": "observation",
      "observation": {
        "success": true,
        "message": "Feedback stored successfully"
      }
    }
    {
      "type": "output",
      "output": "Thank you for your feedback! It will help us improve during this learning phase. Is there anything else you would like help with regarding job posting?"
    }

    ${successfullInteractionPatterns ? `
    ## Successful Interaction Patterns:
    ${successfullInteractionPatterns.map(pattern => `- ${pattern.payload.pattern}`).join("\n")}
    ` : ''}

    ## Negative Interaction Patterns:
    ${negativeInteractionPattern.points?.length > 0 ? `
    ## Negative Interaction Patterns:
    ${negativeInteractionPattern.points.map(p => p.payload.pattern ? `- ${p.payload.pattern}` : '').join("\n")}
    ` : ''}

    ## When User Input is Ambiguous:
    - Politely ask for clarification if a user input is unclear (except for workflow selection after workflows are shown).
    - Use intelligent defaults if the intent is obvious but missing required details (e.g., set numberOpenings to 1 if not provided).

    ## Important:
    - Always ensure ALL salary fields have values (use 1 as default if not provided).
    - Always include authorId, companyId, positionId, industryId, and locationId when calling postJobDetails.
    - **CRITICAL**: ALWAYS ask user to choose between 'draft', 'publish', or 'send for approval' before posting the job.
    - **CRITICAL**: If user chooses approval, ALWAYS call getTeamMembers first, then postJobForApproval with selected approver details.
    - **CRITICAL**: NEVER call postJobDetails when user chooses approval option - ONLY use postJobForApproval for approval flow.
    - **CRITICAL**: When user selects "3" or "approval", your JSON response MUST have "function": "postJobForApproval" NOT "postJobDetails".
    - **CRITICAL**: When calling postJobForApproval, include complete job data from the getJobDetails response, including ALL fields: title, description, shortDescription, skills, benefits, education, employer, aboutCompany, salary ranges, etc.
    - **CRITICAL**: ALWAYS include the user's chosen status in the appropriate posting function call.
    - **CRITICAL**: ALWAYS validate position, industry, and city using getPositionDetails, getIndustryDetails, and getLocationDetails before presenting or posting job details.
    - **CRITICAL**: If getPositionDetails, getIndustryDetails, or getLocationDetails returns {success: false}, you MUST ask the user to provide valid position/industry/city before proceeding.
    - **CRITICAL**: Do NOT call postJobDetails until position, industry, and city validation ALL return {success: true}.
    - **CRITICAL**: NEVER change the original job title. Position, industry, and city validation is ONLY for getting database IDs (positionId, industryId, locationId).
    - **CRITICAL**: When asking for position/industry/city clarification, make it clear this is for categorization only and the job title will remain unchanged.
    - **CRITICAL**: When calling postJobDetails, use the VALIDATED position name (from successful getPositionDetails call) in the position field, NOT the original position name.
    - **CRITICAL**: After ALL validations (position, industry, location) are complete, present the COMPLETE job details including description, salary ranges, skills, benefits, education requirements, and company information - NOT just the validation summary.
    - **CRITICAL**: Use the full job details from getJobDetails response when presenting to user, formatted clearly with all sections visible.
    - **CRITICAL**: When presenting job details, reference the job data that was retrieved from getJobDetails earlier in the conversation - use the actual title, description, salary, skills, benefits, etc. from that response.
    - **CRITICAL**: Do NOT use placeholder text like [Original Title] or [Validated Position] - use the real values from the conversation context.
    - **CRITICAL**: Look back in the conversation history for the getJobDetails observation response and use that actual job data when presenting details to the user.
    - **CRITICAL**: The job details presentation should include the complete generated job description, salary ranges, skills list, benefits, and all other fields from the getJobDetails response.
    - Track and maintain all job details throughout the conversation.
    - Present information in a clear, organized format with proper formatting and sections.
    - Follow up after posting to ensure user satisfaction.
    - Always seek confirmation before making important changes.
    - Always ask user before drafting the job.
    - Do not display calculations in the output.
    - ALWAYS call processUserFeedback when the user provides feedback about the conversation.
    - **CRITICAL**: When user says "yes" to workflow assignment, IMMEDIATELY call getCompanyWorkflows to show available workflows.
    - **CRITICAL**: When user types a workflow name or number after being shown available workflows, ALWAYS call assignWorkflowToJob.
    - **CRITICAL**: If user types a NUMBER (e.g., "20", "1", "5") after showing workflows, treat it as workflow selection by number.
    - **CRITICAL**: If user types "Universal Testing" or any workflow name from the list, IMMEDIATELY call assignWorkflowToJob.
    - **NEVER** say "Please specify a workflow" if workflows were already shown and user provided a selection.
    - **WORKFLOW ASSIGNMENT**: If conversation shows workflows were listed and user types a name OR number, treat it as workflow selection.
    - **NEW WORKFLOW**: If user says "create new workflow" or "new workflow", explain: "To create new workflows, please use our workflow creation feature. For now, you can select from existing workflows or skip workflow assignment."

    ## WORKFLOW SELECTION PATTERNS TO RECOGNIZE:
    - User types workflow name: "Universal Testing", "marketing workflow", "python code workflow"
    - User types workflow number: "1", "20", "5"
    - User types partial name: "universal", "marketing", "python"
    - ALL of these should trigger assignWorkflowToJob function call.

    ## Reflection:
    Before ending a conversation or finalizing job posting, briefly reflect:
    - Did I guide the user clearly?
    - Was all required data (including positionId and industryId) collected and validated?
    - Could anything in this flow be improved next time?
    `
  }
  //Create a Job in database according to the details LLM gives
  async postJobDetails(data) {
    try {

      // Check if this is an approval flow that was misrouted
      if (data && (data.approverId || data.approvalName || data.approvalEmail)) {
        console.error('APPROVAL DATA DETECTED IN postJobDetails! This should use postJobForApproval!');
      }

      // Store the job details in the database
      let positionId = null;
      let industryId = null;
      if (data.companyId && data.authorId) {

        if (data.position) {
          const position = await this.getPositionDetails(data.position);
          if (position && position.success) {
            positionId = position.positionId;
          } else {
            console.warn('WARNING: Position validation failed in postJobDetails. This indicates the LLM did not use the validated position name.');
          }
        } else {
          console.log('No data.position found in data object');
        }

        if (data.industry) {
          const industry = await this.getIndustryDetails(data.industry);
          if (industry && industry.success) {
            industryId = industry.industryId;
          }
        } else {
          console.log('No data.industry found in data object');
        }

        let locationId = null;

        // Try multiple ways to get location information
        const locationSources = [
          data.location,
          data.city,
          data.jobLocation,
          data.locationName
        ];

        // Also check if locationId was passed directly in the data
        if (data.locationId) {
          locationId = data.locationId;
        } else {
          // Try to find location from various possible fields
          for (const locationSource of locationSources) {
            if (locationSource && !locationId) {
              try {
                const location = await this.getLocationDetails(locationSource);
                if (location && location.success) {
                  locationId = location.locationId;
                  break;
                } else {
                  console.log('Location validation response:', JSON.stringify(location, null, 2));
                }
              } catch (error) {
                console.error('Error during location validation:', error.message);
              }
            }
          }

          if (!locationId) {
            const textToSearch = `${data.title || ''} ${data.description || ''} ${data.shortDescription || ''}`.toLowerCase();
            const commonCities = ['new york', 'san francisco', 'los angeles', 'chicago', 'boston', 'seattle', 'austin', 'denver', 'atlanta', 'miami'];

            for (const city of commonCities) {
              if (textToSearch.includes(city) && !locationId) {
                try {
                  const location = await this.getLocationDetails(city);
                  if (location && location.success) {
                    locationId = location.locationId;
                    break;
                  }
                } catch (error) {
                  console.error('Error during text-based location extraction:', error.message);
                }
              }
            }
          }
        }

        if (!positionId) {
          console.error('CRITICAL: positionId is null - job creation will fail');
          return {
            success: false,
            jobId: null,
            message: `Position validation failed. Please ensure the position is properly validated before posting the job.`
          };
        }

        if (!industryId) {
          console.error('CRITICAL: industryId is null - job creation will fail');
          return {
            success: false,
            jobId: null,
            message: `Industry validation failed. Please ensure the industry is properly validated before posting the job.`
          };
        }

        if (!locationId) {
          console.warn('⚠️ WARNING: locationId is null - job will be created without location association');
        }

        // Determine job status based on user input, default to draft
        const jobStatus = data.status === 'publish' ? 'publish' : 'draft';

        const jobData = {
          companyId: data.companyId,
          authorId: data.authorId,
          title: data.title,
          description: data.description,
          shortDescription: data.shortDescription,
          numberOpenings: data.numberOpenings || 1,
          salaryMonthMin: data.salaryMonthMin || 1000,
          salaryMonthMax: data.salaryMonthMax || 100000,
          salaryYearMin: data.salaryYearMin || 100000,
          salaryYearMax: data.salaryYearMax || 1000000,
          salaryHourMin: data.salaryHourMin || 10,
          salaryHourMax: data.salaryHourMax || 100,
          benefits: data.benefits?.length ? data.benefits.map((i, index) => ({ value: i, id: Date.now() + index })) : [],
          experienceMin: data.experienceMin || 0,
          experienceMax: data.experienceMax || 0,
          education: data.education,
          skills: data.skills || [],
          positionId: positionId,
          industryId: industryId,
          linkedin: data.linkedin || "",
          instagram: data.instagram || "",
          twitter: data.twitter || "",
          facebook: data.facebook || "",
          screeningQuestions: [],
          jobBoards: [
            {"id": 13, "name": "uRecruits", "uniqueIntegrationId": "uRecruits08122022"},
            {"id": 14, "name": "Google Jobs", "uniqueIntegrationId": "googleJobs08122022"}
          ],
          applicationForm: [],
          publicSearch: data.publicSearch || true,
          careerPage: false,
          preferableShift: "General Shift",
          jobType: "Full-Time Employees",
          status: jobStatus,
          functionalArea: "Information Technology (IT)",
          noticePeriod: "1 Month",
          employer: data.employer,
          aboutCompany: data.aboutCompany,
        }
        console.log(jobData, 'jobData')
        const job = await this.jobRepository.create(jobData, { include: [Position, Industry] });

        // Associate location with job if locationId is available
        if (locationId) {
          try {
            const location = await this.locationsService.findById(locationId);
            if (location) {
              await job.$add('locations', location);
            } else {
              console.error('Location not found in database for locationId:', locationId);
            }
          } catch (locationError) {
            console.error('Error associating location with job:', locationError.message);
            console.error('LocationError details:', locationError);
          }
        } else {
          console.warn('⚠️ No locationId available for job association. Job will be created without location.');
        }

        // Store successful job posting for learning
        await this.storeSuccessfulJobPosting(job);

        // Create appropriate message based on status
        const statusMessage = jobStatus === 'publish'
          ? `Job "${data.title}" has been published successfully and is now live for candidates to apply.`
          : `Job "${data.title}" has been saved as draft. You can review and publish it later.`;

        return {
          success: true,
          jobId: job.id,
          jobTitle: data.title,
          companyId: data.companyId,
          status: jobStatus,
          message: statusMessage
        };
      }
    } catch (error) {
      console.error(`Error posting job details: ${error.message}`, error.stack);
      return {
        success: false,
        message: `Failed to save job: ${error.message}`
      };
    }
  }

  //Get all workflows for assignment
  async getCompanyWorkflows({ companyId }) {
    try {
      const workflows = await this.workflowRepository.findAll({
        where: { companyId },
        attributes: ['id', 'title'],
        order: [['createdAt', 'DESC']]
      });

      if (!workflows || workflows.length === 0) {
        return {
          success: false,
          message: "No workflows found for your company. You can create workflows first and then assign them to jobs."
        };
      }

      const workflowList = workflows.map(workflow => ({
        id: workflow.id,
        title: workflow.title
      }));

      return {
        success: true,
        workflows: workflowList,
        message: `Found ${workflows.length} workflow(s) available for assignment.`
      };
    } catch (error) {
      console.error(`Error fetching workflows: ${error.message}`, error.stack);
      return {
        success: false,
        message: `Failed to fetch workflows: ${error.message}`
      };
    }
  }

  //Get the most recent job ID from conversation context
  async getRecentJobId(sessionId: string, companyId: number) {
    try {
      // First, try to get the most recent job for this company (simpler and more reliable)
      const recentJob = await this.jobRepository.findOne({
        where: { companyId },
        order: [['createdAt', 'DESC']]
      });

      if (recentJob) {
        return recentJob.id;
      }

      // Fallback: try to get from conversation context (if needed)
      try {
        const response = await this.qdrantClient.scroll("conversations", {
          filter: {
            must: [
              {
                key: "sessionId",
                match: { value: sessionId }
              },
              {
                key: "flowType",
                match: { value: "job" }
              }
            ]
          },
          limit: 1
        });

        if (response.points && response.points.length > 0) {
          const conversation = response.points[0];
          const messages = conversation.payload.messages as any[];

          // Look for the most recent job posting observation
          for (let i = messages.length - 1; i >= 0; i--) {
            const message = messages[i];
            if (message.role === 'assistant' && typeof message.content === 'string') {
              try {
                const parsed = JSON.parse(message.content);
                if (parsed.role === 'observation' && parsed.observation?.jobId) {
                  return parsed.observation.jobId;
                }
              } catch (e) {
                // Continue searching
              }
            }
          }
        }
      } catch (conversationError) {
        console.error(`Error getting conversation context: ${conversationError.message}`);
      }

      return null;
    } catch (error) {
      console.error(`Error getting recent job ID: ${error.message}`, error.stack);
      return null;
    }
  }

  //Assign workflow to job by title or number
  async assignWorkflowToJob({ workflowTitle = null, workflowNumber = null, companyId, sessionId }) {
    try {
      // Get the most recent job ID from conversation context
      const jobId = await this.getRecentJobId(sessionId, companyId);

      if (!jobId) {
        return {
          success: false,
          message: `No recent job found to assign workflow to. Please create a job first.`
        };
      }

      let workflow;

      // If workflowNumber is provided, get workflow by position in list
      if (workflowNumber) {
        const workflows = await this.workflowRepository.findAll({
          where: { companyId },
          order: [['createdAt', 'DESC']]
        });

        const workflowIndex = parseInt(workflowNumber) - 1; // Convert to 0-based index
        if (workflowIndex >= 0 && workflowIndex < workflows.length) {
          workflow = workflows[workflowIndex];
        } else {
          return {
            success: false,
            message: `Invalid workflow number "${workflowNumber}". Please select a number between 1 and ${workflows.length}.`
          };
        }
      } else {
        // Find the workflow by title and companyId
        workflow = await this.workflowRepository.findOne({
          where: {
            title: workflowTitle,
            companyId: companyId
          }
        });
      }

      if (!workflow) {
        const searchTerm = workflowNumber ? `number "${workflowNumber}"` : `"${workflowTitle}"`;
        return {
          success: false,
          message: `Workflow ${searchTerm} not found. Please check the workflow name or number and try again.`
        };
      }

      // Find the job to ensure it exists and belongs to the company
      const job = await this.jobRepository.findOne({
        where: {
          id: jobId,
          companyId: companyId
        }
      });

      if (!job) {
        return {
          success: false,
          message: `Job not found or doesn't belong to your company.`
        };
      }

      // Update the job with the workflow ID
      await this.jobRepository.update(
        { workflowId: workflow.id },
        { where: { id: jobId, companyId: companyId } }
      );

      return {
        success: true,
        message: `Workflow "${workflow.title}" has been successfully assigned to the job "${job.title}".`
      };
    } catch (error) {
      console.error(`Error assigning workflow to job: ${error.message}`, error.stack);
      return {
        success: false,
        message: `Failed to assign workflow: ${error.message}`
      };
    }
  }

  // Get Job suggestion from the llm with similar job and existing jobs scenarios
  async getJobDetails({ title, companyId }) {
    try {
      // Check for existing job under this tenant first
      const existingJob = await this.getExistingJob(title, companyId);
      if (existingJob) {
        return `${existingJob.title} already exists. If you want to modify the existing job, you can do so by following link: "https://app.urecruits.com/job/${existingJob.id}/edit"`;
      }

      // Get similar jobs for reference
      const similarJobs = await this.getSimilarJobs(title);

      // Format similar jobs for context
      const similarJobsString = similarJobs.map(job => `- 
      Title: ${job.title}
      Description: ${job.description || 'Not specified'}
      Position: ${job.position || 'Not specified'}
      Industry: ${job.industry || 'Not specified'}
      Experience: ${job.experienceMin || 0}-${job.experienceMax || 5} years
      Salary Ranges: 
      - Hourly: $${job.salaryHourMin || 1}-$${job.salaryHourMax || 50}/hour
      - Monthly: $${job.salaryMonthMin || 1}-$${job.salaryMonthMax || 8000}/month
      - Yearly: $${job.salaryYearMin || 1}-$${job.salaryYearMax || 100000}/year
      Benefits: ${Array.isArray(job.benefits) ? job.benefits.join(", ") : 'Not specified'}
      Education: ${job.education || 'Not specified'}
      Location: ${Array.isArray(job.locations) ? job.locations.map(loc => `${loc.city}, ${loc.state}`).join(", ") : 'Not specified'}
      Skills: ${Array.isArray(job.skills) ? job.skills.join(", ") : 'Not specified'}
      Functional Area: ${job.functionalArea || 'Not specified'}
    `).join("\n\n");

      const company = companyId && await this.companyRepository.findByPk(companyId, { include: [Industry] });
      if (!company) {
        throw new Error("Company is not listed.");
      }

      // Get learning insights from successful job postings
      const learningInsights = await this.enhanceJobDetailsWithLearning(title, company, similarJobsString);

      // Enhanced prompt for job generation
      const response = await this.llm.invoke(`
      I need you to generate a comprehensive and accurate job posting for a "${title}" position at ${company.name}. This job posting must reflect current industry standards and market conditions as of 2023.

      ## Job Posting Context:
      - Company: ${company.name} (${company.about ? `About: ${company.about}` : ''}) // if company.about is not provided, Generate general company information according its industry: ${company.industry?.label}
      - Position: ${title}
      - Region: United States (adjust salary and benefits accordingly)
      - Current Year: ${new Date().getFullYear()} (ensure all information is up-to-date)

      ## Required Information (ALL fields must be completed):

      1. Job Title:
         - MUST use exactly: "${title}" (do not change this title)
         - This is the exact title requested by the user and must not be modified

      2. Detailed Description:
         - Begin with a compelling company and role overview (3-4 sentences)
         - Use HTML formatting for readability (<p>, <ul>, <li>, <strong>)
         - Include detailed section on day-to-day responsibilities
         - Include detailed section of role's requirements
         - Mention reporting structure and team collaboration aspects
         - Describe the impact this role has on business outcomes
         - Keep within 250-300 words for optimal readability

      3. Short Description:
         - Create a concise 2-3 sentence summary that highlights:
         - The primary responsibilities
         - The key value proposition
         - What makes this role unique at ${company.name}

      4. Experience Requirements:
         - Should be in the range of 0-50 years and should be in integer range
         - Specify minimum years of relevant experience (be realistic for the role)
         - Specify maximum years to avoid age discrimination
         - Include specific domain experience if necessary
         - Consider both technical and soft skills experience

      5. Precise Compensation Package (MUST include ALL ranges):
         - Hourly rate range in USD (e.g., $25-$40/hour) - REQUIRED
         - Monthly salary range in USD (e.g., $4000-$6500/month) - REQUIRED
         - Annual salary range in USD (e.g., $48,000-$78,000/year) - REQUIRED
         - Ensure ranges are competitive based on:
           * Current industry standards for ${title}
           * Experience level requirements
           * Geographic considerations
           * Company size and industry

      6. Required Skills:
         - List 7-10 specific technical skills relevant to the position
         - Include a mix of hard and soft skills
         - Specify proficiency levels where applicable
         - Format as JSON array: ["Skill1", "Skill2", "Skill3"]
         - Include both required and preferred skills

      7. Position Details:
         - Specific job title that aligns with industry standards
         - Include any specializations or focus areas

      8. Industry Classification:
         - Primary industry relevant to ${company.name}
         - Sub-industry or specialization

      9. Comprehensive Benefits Package:
         - Health insurance details (medical, dental, vision)
         - Retirement benefits (401(k), matching, etc.)
         - Paid time off policy
         - Professional development opportunities
         - Work-life balance benefits
         - Format as JSON array with detailed descriptions
         - Include at least 5-8 specific benefits

      10. Education Requirements:
          - Minimum education level required
          - Preferred education level
          - Should be one of the following from the given list of education levels: ${Object.values(EducationLevel).join(" | ")}

      11. Company Information:
          - Company Name: "${company.name}"
          - Company ID: ${company.id}
          - Company About: "${company.about}"//if company about is not provided, then generate a new one according to its industry: ${company.industry?.label}
          - Website: "${company.website || 'Not provided'}"

      ## Reference Information:

      ### Similar Jobs in the Industry:
      ${similarJobsString}

      ${learningInsights ? `
      ## Learning Insights from Successful Job Postings:
      ${learningInsights.successfulPatterns}
      ` : ''}

      ## Output Requirements:
      - Return a complete JSON object following the example format below
      - ENSURE all salary fields are included and realistic (hourly, monthly, and yearly)
      - If exact figures aren't certain, provide reasonable estimates based on industry standards
      - ALL fields in the example must be included in your response
      - Format skills and benefits as proper JSON arrays
      - Use HTML formatting for the description field
      - Keep the formatting clean and professional

      ## Example Format:
      {
        "title": "Senior Software Engineer",
        "description": "<p>As a Senior Software Engineer at ${company.name}, you will lead the development of innovative software solutions...</p><p><strong>Key Responsibilities:</strong></p><ul><li>Lead technical design and architecture decisions</li><li>Mentor junior developers and conduct code reviews</li><li>Collaborate with product managers on feature specifications</li><li>Implement best practices for code quality and testing</li><li>Drive technical innovation and process improvements</li></ul>",
        "shortDescription": "Lead software engineer role focusing on architecture, mentoring, and technical innovation",
        "experienceMin": 5,
        "experienceMax": 8,
        "salaryHourMin": 75,
        "salaryHourMax": 120,
        "salaryMonthMin": 12000,
        "salaryMonthMax": 19200,
        "salaryYearMin": 144000,
        "salaryYearMax": 230400,
        "skills": ["JavaScript", "React", "Node.js", "AWS", "System Design", "Team Leadership"],
        "position": "Senior Software Engineer",
        "industry": "Technology",
        "benefits": [
          "Comprehensive health insurance including dental and vision",
          "401(k) with 6% company match",
          "Unlimited PTO with minimum 15 days",
          "Annual professional development allowance",
          "Remote work flexibility",
          "Stock options",
          "Parental leave",
          "Wellness programs"
        ],
        "education": "Bachelor's degree",
        "companyId": ${company.id},
        "employer": "${company.name}",
        "aboutCompany": "${company.about}"//
      }

      IMPORTANT: Generate a complete job posting following this exact format. Ensure ALL fields are filled with appropriate, detailed, and realistic information. Every field in the example MUST be included in your response, with particular attention to ALL salary ranges (hourly, monthly, and yearly).
    `);

      return response;
    } catch (error) {
      console.error("Error fetching job details:", error);
      return null;
    }
  }

  //Clean job data for job extraction using langchain service  
  cleanJobData(jobData) {
    let str = jobData;
    if (typeof jobData !== 'string') {
      str = JSON.stringify(jobData);  // Convert to string if it's not already
    }
    // Replace markdown-like syntax with plain text or structured format
    const cleaned = str
      .replace(/\*\*(.*?)\*\*/g, '$1')  // Remove bold text syntax (e.g. **Title** -> Title)
      .replace(/\n/g, ' ')  // Remove newlines to make the string easier to parse
      .replace(/\s+/g, ' ')  // Collapse multiple spaces into one
      .trim();

    return cleaned;
  }

  //Format job details using langchain
  async formatJobDetails(jobData, schema = zodSchema) {
    try {
      // Clean up any markdown-like syntax and format the response as string
      const cleanedData = this.cleanJobData(jobData);
      const chain = createExtractionChainFromZod(schema, this.chatModel);
      const extractedData = await chain.invoke({ input: cleanedData });

      const data = extractedData?.text;
      let position = null;
      if (data[0]?.position) {
        position = await this.getPositionDetails(data[0].position);
        if (position && position.success) {
          data[0].position = position.position;
        }
      }
      let industry = null;
      if (data[0]?.industry) {
        industry = await this.getIndustryDetails(data[0].industry);
        if (industry && industry.success) {
          data[0].industry = industry.industry;
        }
      }
      let location = null;
      if (data[0]?.location) {
        location = await this.getLocationDetails(data[0].location);
        if (location && location.success) {
          data[0].location = location.location;
        }
      }
      return data;
    } catch (error) {
      console.error("Error formatting job details:", error);
      return null;
    }
  }

  //Get position details
  async getPositionDetails(prompt: string | any) {
    try {
      // Handle both string input and object input from LangChain
      let positionString: string;

      if (typeof prompt === 'string') {
        positionString = prompt;
      } else if (prompt && typeof prompt === 'object' && prompt.position) {
        positionString = prompt.position;
      } else {
        return {
          success: false,
          positionId: null,
          position: null,
          message: `Invalid position input. Please specify a valid job position (e.g., Software Engineer, Marketing Manager, Data Analyst).`
        };
      }

      // Validate and sanitize input
      if (!positionString || typeof positionString !== 'string') {
        return {
          success: false,
          positionId: null,
          position: null,
          message: `Invalid position input. Please specify a valid job position (e.g., Software Engineer, Marketing Manager, Data Analyst).`
        };
      }

      const cleanPrompt = positionString.trim();
      if (!cleanPrompt) {
        return {
          success: false,
          positionId: null,
          position: null,
          message: `Empty position input. Please specify a valid job position (e.g., Software Engineer, Marketing Manager, Data Analyst).`
        };
      }

      // First, try direct database search using the positions service
      const dbPositions = await this.positionsService.findByLabel(cleanPrompt);

      if (dbPositions && dbPositions.length > 0) {
        // Find the best match - exact match first, then partial match
        let bestMatch = dbPositions.find(pos =>
          pos.label.toLowerCase() === cleanPrompt.toLowerCase()
        );

        if (!bestMatch) {
          bestMatch = dbPositions.find(pos =>
            pos.label.toLowerCase().includes(cleanPrompt.toLowerCase()) ||
            cleanPrompt.toLowerCase().includes(pos.label.toLowerCase())
          );
        }

        if (!bestMatch) {
          bestMatch = dbPositions[0];
        }

        return {
          success: true,
          positionId: bestMatch.id,
          position: bestMatch,
          message: `Position '${bestMatch.label}' found and validated.`
        };
      }

      // Fallback to vector search if database search fails
      const vector = await this.langchainService.getEmbeddings(cleanPrompt);
      const response = await this.qdrantClient.search("positions", {
        vector,
        limit: 5,
        score_threshold: 0.6
      })
      const positions = response.sort((a, b) => b.score - a.score).map(result => ({
        ...result.payload,
      }));

      if (positions.length > 0 && positions[0]) {
        return {
          success: true,
          positionId: positions[0].id,
          position: positions[0],
          message: `Position '${positions[0].label || cleanPrompt}' found and validated.`
        };
      } else {
        return {
          success: false,
          positionId: null,
          position: null,
          message: `Position '${cleanPrompt}' not found. Please specify a valid job position (e.g., Software Engineer, Marketing Manager, Data Analyst).`
        };
      }
    } catch (error) {
      console.error(`Error getting position details: ${error.message}`, error.stack);
      return {
        success: false,
        positionId: null,
        position: null,
        message: `Error validating position. Please try again.`
      };
    }
  }

  //Get industry details
  async getIndustryDetails(prompt: string | any) {
    try {
      // Handle both string input and object input from LangChain
      let industryString: string;

      if (typeof prompt === 'string') {
        industryString = prompt;
      } else if (prompt && typeof prompt === 'object' && prompt.industry) {
        industryString = prompt.industry;
      } else {
        return {
          success: false,
          industryId: null,
          industry: null,
          message: `Invalid industry input. Please specify a valid industry (e.g., Information Technology, Healthcare, Finance).`
        };
      }

      // Validate and sanitize input
      if (!industryString || typeof industryString !== 'string') {
        return {
          success: false,
          industryId: null,
          industry: null,
          message: `Invalid industry input. Please specify a valid industry (e.g., Information Technology, Healthcare, Finance).`
        };
      }

      const cleanPrompt = industryString.trim();
      if (!cleanPrompt) {
        return {
          success: false,
          industryId: null,
          industry: null,
          message: `Empty industry input. Please specify a valid industry (e.g., Information Technology, Healthcare, Finance).`
        };
      }

      // First, try direct database search using the industries service
      let dbIndustries = await this.industriesService.findByLabel(cleanPrompt);

      // Special handling for common industry mappings - try alternative searches
      if (!dbIndustries || dbIndustries.length === 0) {
        const lowerPrompt = cleanPrompt.toLowerCase();
        if (lowerPrompt.includes('cyber') || lowerPrompt.includes('security') ||
            lowerPrompt.includes('tech') || lowerPrompt.includes('software') ||
            lowerPrompt.includes('it')) {
          // Search for Information Technology specifically
          dbIndustries = await this.industriesService.findByLabel('Information Technology');
        } else if (lowerPrompt.includes('health') || lowerPrompt.includes('medical')) {
          dbIndustries = await this.industriesService.findByLabel('Medical & Healthcare');
        } else if (lowerPrompt.includes('finance') || lowerPrompt.includes('bank')) {
          dbIndustries = await this.industriesService.findByLabel('Banking & Finance');
        } else if (lowerPrompt.includes('education') || lowerPrompt.includes('training')) {
          dbIndustries = await this.industriesService.findByLabel('Education & Training');
        } else if (lowerPrompt.includes('construction') || lowerPrompt.includes('building')) {
          dbIndustries = await this.industriesService.findByLabel('Building & Construction');
        }
      }

      if (dbIndustries && dbIndustries.length > 0) {
        // Find the best match - exact match first, then partial match
        let bestMatch = dbIndustries.find(ind =>
          ind.label.toLowerCase() === cleanPrompt.toLowerCase()
        );

        if (!bestMatch) {
          // If no exact match, find the closest partial match
          bestMatch = dbIndustries.find(ind =>
            ind.label.toLowerCase().includes(cleanPrompt.toLowerCase()) ||
            cleanPrompt.toLowerCase().includes(ind.label.toLowerCase())
          );

          // Special handling for common industry mappings
          if (!bestMatch) {
            const lowerPrompt = cleanPrompt.toLowerCase();
            if (lowerPrompt.includes('cyber') || lowerPrompt.includes('security') ||
                lowerPrompt.includes('tech') || lowerPrompt.includes('software') ||
                lowerPrompt.includes('it') || lowerPrompt.includes('information')) {
              bestMatch = dbIndustries.find(ind =>
                ind.label.toLowerCase().includes('information technology')
              );
            }
          }
        }

        if (!bestMatch) {
          bestMatch = dbIndustries[0]; // Take the first result if no good match
        }

        return {
          success: true,
          industryId: bestMatch.id,
          industry: bestMatch,
          message: `Industry '${bestMatch.label}' found and validated.`
        };
      }

      // Fallback to vector search if database search fails
      const vector = await this.langchainService.getEmbeddings(cleanPrompt);
      const response = await this.qdrantClient.search("industries", {
        vector,
        limit: 5,
        score_threshold: 0.6 // Lowered threshold for better matching
      })
      const industries = response.sort((a, b) => b.score - a.score).map(result => ({
        ...result.payload,
      }));

      if (industries.length > 0 && industries[0]) {
        return {
          success: true,
          industryId: industries[0].id,
          industry: industries[0],
          message: `Industry '${industries[0].label || cleanPrompt}' found and validated.`
        };
      } else {
        // Provide specific suggestions based on common industry mappings
        let suggestion = '';
        const lowerPrompt = cleanPrompt.toLowerCase();

        if (lowerPrompt.includes('cyber') || lowerPrompt.includes('security') || lowerPrompt.includes('tech') || lowerPrompt.includes('software') || lowerPrompt.includes('it')) {
          suggestion = ' Cyber Security roles typically fall under "Information Technology". Please use "Information Technology" as the industry.';
        } else if (lowerPrompt.includes('health') || lowerPrompt.includes('medical')) {
          suggestion = ' Did you mean "Medical & Healthcare"?';
        } else if (lowerPrompt.includes('finance') || lowerPrompt.includes('bank')) {
          suggestion = ' Did you mean "Banking & Finance"?';
        } else if (lowerPrompt.includes('education') || lowerPrompt.includes('training')) {
          suggestion = ' Did you mean "Education & Training"?';
        } else if (lowerPrompt.includes('construction') || lowerPrompt.includes('building')) {
          suggestion = ' Did you mean "Building & Construction"?';
        }

        return {
          success: false,
          industryId: null,
          industry: null,
          message: `Industry '${cleanPrompt}' not found.${suggestion} Available industries include: Information Technology, Medical & Healthcare, Banking & Finance, Engineering, Education & Training, Government, Consulting & Professional Services, Building & Construction, Digital Media, Energy Mining & Resources, Insurance, Media, Telecommunications, Hospitality & Tourism, Automobile, Chemical, Public services, Retail, Transport.`
        };
      }
    } catch (error) {
      console.error(`Error getting industry details: ${error.message}`, error.stack);
      return {
        success: false,
        industryId: null,
        industry: null,
        message: `Error validating industry. Please try again.`
      };
    }
  }

  //Get location details
  async getLocationDetails(prompt: string | any) {
    try {
      // Handle both string input and object input from LangChain
      let cityString: string;

      if (typeof prompt === 'string') {
        cityString = prompt;
      } else if (prompt && typeof prompt === 'object' && prompt.city) {
        cityString = prompt.city;
      } else if (prompt && typeof prompt === 'object' && prompt.location) {
        cityString = prompt.location;
      } else {
        return {
          success: false,
          locationId: null,
          location: null,
          message: `Invalid city input. Please specify a valid city name (e.g., 'New York' or 'San Francisco').`
        };
      }

      // Validate and sanitize input
      if (!cityString || typeof cityString !== 'string') {
        return {
          success: false,
          locationId: null,
          location: null,
          message: `Invalid city input. Please specify a valid city name (e.g., 'New York' or 'San Francisco').`
        };
      }

      const cleanPrompt = cityString.trim();
      if (!cleanPrompt) {
        return {
          success: false,
          locationId: null,
          location: null,
          message: `Empty city input. Please specify a valid city name (e.g., 'New York' or 'San Francisco').`
        };
      }

      // Extract city name if input contains comma (e.g., "San Fidel, New Mexico" -> "San Fidel")
      const cityName = cleanPrompt.includes(',') ? cleanPrompt.split(',')[0].trim() : cleanPrompt;

      // Use the locations service to search for the city
      // First try with the full input (handles "City, State" format)
      let dbLocations = await this.locationsService.findByCityWithLimit({
        find: cleanPrompt,
        offset: 0
      });

      // If no results and input contains comma, try with just the city part
      if ((!dbLocations || dbLocations.length === 0) && cleanPrompt.includes(',')) {
        dbLocations = await this.locationsService.findByCity(cityName);
      }

      // If still no results, try the original findByCity method with full input
      if (!dbLocations || dbLocations.length === 0) {
        dbLocations = await this.locationsService.findByCity(cleanPrompt);
      }

      if (dbLocations && dbLocations.length > 0) {

        // Find the best match - exact match first, then partial match
        let bestMatch = dbLocations.find(loc =>
          loc.city.toLowerCase() === cleanPrompt.toLowerCase() ||
          loc.city.toLowerCase() === cityName.toLowerCase()
        );

        if (!bestMatch) {
          bestMatch = dbLocations.find(loc =>
            loc.city.toLowerCase().includes(cleanPrompt.toLowerCase()) ||
            cleanPrompt.toLowerCase().includes(loc.city.toLowerCase()) ||
            loc.city.toLowerCase().includes(cityName.toLowerCase()) ||
            cityName.toLowerCase().includes(loc.city.toLowerCase())
          );
        }

        if (!bestMatch) {
          bestMatch = dbLocations[0];
        }

        console.log(`[LocationValidation] Best match found: ${bestMatch.city}, ${bestMatch.state} (ID: ${bestMatch.id})`);

        return {
          success: true,
          locationId: bestMatch.id,
          location: bestMatch,
          message: `City '${bestMatch.city}, ${bestMatch.state}' found and validated.`
        };
      }

      console.log(`[LocationValidation] No matches found for: "${cleanPrompt}" (city: "${cityName}")`);

      return {
        success: false,
        locationId: null,
        location: null,
        message: `City '${cleanPrompt}' not found. Please specify a valid city name (e.g., 'New York' or 'San Francisco').`
      };
    } catch (error) {
      console.error(`Error getting location details: ${error.message}`, error.stack);
      return {
        success: false,
        locationId: null,
        location: null,
        message: `Error validating city. Please try again.`
      };
    }
  }

  //Get team members for approval
  async getTeamMembers(prompt: string | any) {
    try {
      // Handle both string input and object input from LangChain
      let companyId: number;

      if (typeof prompt === 'string') {
        companyId = parseInt(prompt);
      } else if (prompt && typeof prompt === 'object' && prompt.companyId) {
        companyId = prompt.companyId;
      } else {
        return {
          success: false,
          teamMembers: [],
          message: `Invalid input for getting team members. Company ID is required.`
        };
      }

      // Validate companyId
      if (!companyId || isNaN(companyId)) {
        return {
          success: false,
          teamMembers: [],
          message: `Invalid company ID. Please provide a valid company ID.`
        };
      }

      // Get team members using the users service
      const teamMembers = await this.usersService.getLeadUsers(companyId, 0, {});

      if (teamMembers && teamMembers.length > 0) {
        const formattedMembers = teamMembers.map((member, index) => ({
          id: member.id,
          name: `${member.firstname} ${member.lastname}`.trim(),
          email: member.email,
          displayNumber: index + 1
        }));

        return {
          success: true,
          teamMembers: formattedMembers,
          message: `Found ${formattedMembers.length} team members available for approval.`
        };
      }

      return {
        success: false,
        teamMembers: [],
        message: `No team members found for approval. Please contact your administrator.`
      };
    } catch (error) {
      console.error(`Error getting team members: ${error.message}`, error.stack);
      return {
        success: false,
        teamMembers: [],
        message: `Error retrieving team members. Please try again.`
      };
    }
  }

  //Post job for approval
  async postJobForApproval(prompt: string | any) {
    try {
      let jobData: any;
      let approverId: number;
      let approverName: string;
      let approverEmail: string;

      if (typeof prompt === 'string') {
        try {
          const parsed = JSON.parse(prompt);
          jobData = parsed.jobData || parsed;
          approverId = parsed.approverId;
          approverName = parsed.approverName;
          approverEmail = parsed.approverEmail;
        } catch (parseError) {
          return {
            success: false,
            jobId: null,
            message: `Invalid input format. Please provide valid job data and approver details.`
          };
        }
      } else if (prompt && typeof prompt === 'object') {
        jobData = prompt.jobData || prompt;
        approverId = prompt.approverId;
        approverName = prompt.approverName;
        approverEmail = prompt.approverEmail;
      } else {
        return {
          success: false,
          jobId: null,
          message: `Invalid input. Please provide job data and approver details.`
        };
      }

      // Validate required approver details
      if (!approverId || !approverName || !approverEmail) {
        return {
          success: false,
          jobId: null,
          message: `Missing approver details. Please provide approver ID, name, and email.`
        };
      }

      // Validate that we have essential job data
      if (!jobData || !jobData.title) {
        return {
          success: false,
          jobId: null,
          message: `Incomplete job data provided. Please ensure the job title and other details are included.`
        };
      }

      const jobDataWithApproval = {
        ...jobData,
        status: 'draft',
        approverId: approverId,
        approvalName: approverName,
        approvalEmail: approverEmail
      };

      console.log('Posting job for approval with data:', jobDataWithApproval);

      const result = await this.postJobDetails(jobDataWithApproval);

      if (result && result.success) {
        return {
          success: true,
          jobId: result.jobId,
          message: `Job "${jobData.title}" has been sent for approval to ${approverName} (${approverEmail}). They will receive an email notification to review and approve the job posting.`
        };
      } else {
        return {
          success: false,
          jobId: null,
          message: result?.message || `Failed to send job for approval. Please try again.`
        };
      }
    } catch (error) {
      console.error(`Error posting job for approval: ${error.message}`, error.stack);
      return {
        success: false,
        jobId: null,
        message: `Error sending job for approval. Please try again.`
      };
    }
  }

  //Get similar jobs
  async getSimilarJobs(jobTitle) {
    const vector = await this.langchainService.getEmbeddings(jobTitle);
    const response = await this.qdrantClient.search("jobs", {
      vector,
      limit: 10,
      score_threshold: 0.9
    })
    const jobs = response.sort((a, b) => b.score - a.score).map(result => ({
      ...result.payload,
    }));
    return jobs;
  }

  //Get existing job
  async getExistingJob(title, companyId) {
    const jobs = await this.qdrantClient.scroll('jobs', {
      filter: {
        must: [
          { key: "companyId", match: { value: companyId } },
          { key: "title", match: { value: title } }
        ]
      }
    })

    return jobs.points[0]?.payload;
  }

  //Start conversation with the chatbot
  async startConversation(body: { sessionId: string; userInput: string }, companyId: number, userId: number) {
    try {
      const { sessionId, userInput } = body;
      const company = companyId && await this.companyRepository.findByPk(companyId);
      if (!company) return { chatResponse: "Company not found.", formattedJobDetails: null };

      // EARLY WORKFLOW ASSIGNMENT DETECTION
      // Check if this is a workflow assignment by number or name
      const isWorkflowNumber = /^\d+$/.test(userInput.trim());
      const isWorkflowAssignmentContext = await this.checkWorkflowAssignmentContext(sessionId, companyId);

      // Only trigger workflow assignment if:
      // 1. We're in workflow assignment context AND
      // 2. Input is ONLY a number (for workflow selection by number) AND
      // 3. Input is not a job-related term
      const jobRelatedTerms = [
        'developer', 'engineer', 'manager', 'analyst', 'designer', 'architect', 'lead', 'senior', 'junior',
        'specialist', 'consultant', 'coordinator', 'director', 'supervisor', 'administrator', 'technician',
        'programmer', 'scientist', 'researcher', 'assistant', 'associate', 'executive', 'officer',
        'create job', 'post job', 'new job', 'job posting', 'job creation', 'make job', 'add job'
      ];
      const workflowCreationKeywords = ['create workflow', 'new workflow', 'workflow creation', 'make workflow', 'build workflow'];
      const negativeResponses = ['no', 'nope', 'not now', 'skip', 'cancel', 'exit'];

      const containsJobRelatedTerms = jobRelatedTerms.some(term =>
        userInput.toLowerCase().includes(term)
      );
      const containsWorkflowCreationKeywords = workflowCreationKeywords.some(keyword =>
        userInput.toLowerCase().includes(keyword)
      );
      const isNegativeResponse = negativeResponses.some(response =>
        userInput.toLowerCase().trim().startsWith(response)
      );

      // ONLY trigger workflow assignment for pure numbers or explicit workflow names (not job titles)
      if (isWorkflowAssignmentContext &&
        !containsJobRelatedTerms &&
        !containsWorkflowCreationKeywords &&
        !isNegativeResponse &&
        isWorkflowNumber) {

        const result = await this.assignWorkflowToJob({
          workflowNumber: userInput.trim(),
          companyId,
          sessionId
        });
        return { chatResponse: result.message, formattedJobDetails: null };
      }

      const conversationHistory = await this.getConversationHistory(sessionId);
      const messages = conversationHistory?.messages?.length ?
        conversationHistory.messages :
        [new SystemMessage(await this.getSystemPrompt(company, userId))];

      messages.push(new HumanMessage(JSON.stringify({ type: "user", user: userInput })));

      let formattedJobDetails = null;
      let observation = null;
      let lastJobDetails = null;

      while (true) {
        // Truncate messages to prevent token limit issues
        const maxMessages = 15; // Keep conversation manageable
        const truncatedMessages = messages.length > maxMessages
          ? [messages[0], ...messages.slice(-maxMessages + 1)] // Keep system message + recent messages
          : messages;

        const chat = await this.chatModel.invoke(truncatedMessages);
        const data = chat.content;
        const jsonPattern = /\{(?:[^{}]|\{(?:[^{}]|\{[^{}]*\})*\})*\}/g;
        const matches = typeof data === 'string' ? data.match(jsonPattern) : null;
        let call;
        let processedContent = data;

        if (matches?.length) {
          try {
            call = JSON.parse(matches[matches.length - 1]);
            processedContent = matches[matches.length - 1];
          } catch (parseError) {
            console.error("Error parsing extracted JSON:", parseError);
          }
        }

        try {
          if (!call) {
            call = typeof processedContent === 'string' ? JSON.parse(processedContent) : processedContent;
          }
          if (!call.type) {
            call = { type: "output", output: processedContent };
          }
        } catch (error) {
          call = { type: "output", output: processedContent };
        }

        messages.push(new AIMessage(typeof processedContent === 'string' ? processedContent : JSON.stringify(processedContent)));

        if (typeof data === 'string' && data.includes('"function":"getJobDetails"')) {
          const match = data.match(/\{"type":"action","function":"([^"]+)","input":(\{[^}]+\})\}/);
          if (match?.[1] === "getJobDetails") {
            try {
              const inputJson = JSON.parse(match[2]);
              lastJobDetails = await this.getJobDetails(inputJson);
              observation = lastJobDetails;
              messages.push(new AIMessage(JSON.stringify({ role: "observation", observation })));
              call = { type: "continue" };
            } catch (extractError) {
              console.error("Error extracting function call from text:", extractError);
            }
          }
        }

        if (call.type === "plan" || call.type === "continue") continue;

        if (call.type === "action" && call.function && call.input) {
          if (call.function === "processUserFeedback") {
            call.input.sessionId = sessionId;
          }
          if (call.function === "assignWorkflowToJob") {
            call.input.sessionId = sessionId;
            call.input.companyId = companyId;

            // Check if user provided a number instead of workflow name
            const isNumber = /^\d+$/.test(userInput.trim());
            if (isNumber && !call.input.workflowNumber) {
              call.input.workflowNumber = userInput.trim();
              delete call.input.workflowTitle; // Remove title if number is provided
            }
          }

          if (call.function === "postJobDetails" && call.input && call.input.status === 'draft') {
            const recentMessages = messages.slice(-10).map(m => m.content).join(' ').toLowerCase();
            if (recentMessages.includes('approval') || recentMessages.includes('team member') || recentMessages.includes('approver')) {
              console.error('Function input:', JSON.stringify(call.input, null, 2));
            }
          }

          observation = await this[call.function](call.input);
          if (call.function === "getJobDetails") lastJobDetails = observation;
          messages.push(new AIMessage(JSON.stringify({ role: "observation", observation })));
          continue;
        }

        if (call.type === "output") {
          // CRITICAL FIX: Check if this is a workflow assignment context and user provided input
          const isWorkflowAssignmentContext = messages.some(msg =>
            msg.content && typeof msg.content === 'string' &&
            msg.content.includes('Here are your available workflows:')
          );

          const isGenericWorkflowResponse = call.output.includes('Please specify a workflow to attach to the job') ||
            call.output.includes('specify a workflow') ||
            call.output.includes('create a new one') ||
            call.output.includes('Please specify a workflow');

          if (isWorkflowAssignmentContext && isGenericWorkflowResponse) {

            // Force workflow assignment
            const isNumber = /^\d+$/.test(userInput.trim());
            if (isNumber) {
              const result = await this.assignWorkflowToJob({
                workflowNumber: userInput.trim(),
                companyId,
                sessionId
              });
              return { chatResponse: result.message, formattedJobDetails };
            } else {
              const result = await this.assignWorkflowToJob({
                workflowTitle: userInput.trim(),
                companyId,
                sessionId
              });
              return { chatResponse: result.message, formattedJobDetails };
            }
          }

          await this.storeConversationInQdrant({ sessionId: sessionId, companyId, messages, userId, timestamp: new Date().toISOString() });

          const isJobRelated = call.output.toLowerCase().includes('job') || call.output.toLowerCase().includes('position') ||
            call.output.toLowerCase().includes('experience') || call.output.toLowerCase().includes('salary') ||
            call.output.toLowerCase().includes('benefits') || call.output.toLowerCase().includes('education') ||
            call.output.toLowerCase().includes('skills') || call.output.toLowerCase().includes('description');

          // Check if this is a job details presentation after validation
          const isJobDetailsPresentation = call.output.toLowerCase().includes('job title:') ||
            call.output.toLowerCase().includes('position classification:') ||
            call.output.toLowerCase().includes('complete job details') ||
            call.output.toLowerCase().includes('job posting');

          if (isJobRelated && observation) {
            formattedJobDetails = await this.formatJobDetails(observation);
          } else if (isJobDetailsPresentation && lastJobDetails) {
            // Use the last job details for formatting when presenting complete job details
            formattedJobDetails = await this.formatJobDetails(lastJobDetails);
          }

          return { chatResponse: call.output, formattedJobDetails };
        }

        break;
      }

      return { chatResponse: "I'm sorry, I couldn't process your request. Please try again.", formattedJobDetails: null };

    } catch (error) {
      console.error("Error in startConversation:", error);
      throw error;
    }
  }

  //Store conversation in Qdrant
  private async storeConversationInQdrant(data: {
    sessionId: string;
    companyId: number;
    messages: BaseMessage[];
    timestamp: string;
    userId: number;
  }) {
    try {
      // Truncate conversation to avoid token limits
      const recentMessages = data.messages.slice(-10); // Keep only last 10 messages
      const conversationStr = recentMessages
        .map(msg => {
          const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
          // Truncate very long messages
          const truncatedContent = content.length > 500 ? content.substring(0, 500) + '...' : content;
          return `${msg._getType()}: ${truncatedContent}`;
        })
        .join('\n');

      // Ensure conversation string is not too long for embeddings
      const maxLength = 6000; // Safe limit for embeddings
      const finalConversationStr = conversationStr.length > maxLength
        ? conversationStr.substring(0, maxLength) + '...'
        : conversationStr;

      let vector;
      try {
        vector = await this.langchainService.getEmbeddings(finalConversationStr);
      } catch (embeddingError) {
        console.error("Error generating embedding, using fallback:", embeddingError.message);
        // Create a simple fallback vector (zeros) if embedding fails
        vector = new Array(1536).fill(0); // OpenAI ada-002 embedding size
      }

      const point = {
        id: data.sessionId,
        vector,
        payload: {
          sessionId: data.sessionId,
          companyId: data.companyId,
          userId: data.userId,
          flowType: "job",
          messages: data.messages.map(msg => ({
            role: msg._getType(),
            content: typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content)
          })),
          timestamp: data.timestamp
        }
      };

      await this.qdrantClient.upsert("conversations", {
        wait: true,
        points: [point]
      });

    } catch (error) {
      console.error("Error storing conversation in Qdrant:", error);
    }
  }

  //Check if conversation context indicates workflow assignment
  private async checkWorkflowAssignmentContext(sessionId: string, companyId: number): Promise<boolean> {
    try {
      const conversationHistory = await this.getConversationHistory(sessionId);
      if (!conversationHistory || !conversationHistory.messages?.length) {
        return false;
      }

      // Look for workflow list in recent conversation
      const recentMessages = conversationHistory.messages.slice(-10);
      const hasWorkflowList = recentMessages.some(msg => {
        const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
        return content.includes('Here are your available workflows:') ||
          content.includes('Which workflow would you like to assign') ||
          content.includes('available workflows for assignment') ||
          content.includes('workflow(s) available for assignment');
      });

      return hasWorkflowList;
    } catch (error) {
      console.error('[JobChatbot] Error checking workflow assignment context:', error);
      return false;
    }
  }

  //Get conversation history
  private async getConversationHistory(sessionId: string) {
    try {
      // First, check for reset markers
      const resetResponse = await this.qdrantClient.scroll("conversations", {
        filter: {
          must: [
            {
              key: "sessionId",
              match: {
                value: sessionId,
              },
            },
            {
              key: "flowType",
              match: {
                value: "chat_reset",
              },
            },
          ],
        },
        limit: 10,
      });

      let lastResetTimestamp: string | null = null;
      if (resetResponse.points && resetResponse.points.length > 0) {
        const resetMarkers = resetResponse.points
          .filter(point => point.payload?.isResetMarker === true)
          .sort((a, b) => new Date(b.payload?.resetTimestamp as string).getTime() - new Date(a.payload?.resetTimestamp as string).getTime());

        if (resetMarkers.length > 0) {
          lastResetTimestamp = resetMarkers[0].payload?.resetTimestamp as string;
        }
      }

      // Get job conversation history
      const response = await this.qdrantClient.scroll("conversations", {
        filter: {
          must: [
            {
              key: "sessionId",
              match: {
                value: sessionId,
              },
            },
            {
              key: "flowType",
              match: {
                value: "job",
              },
            },
          ],
        },
        limit: 1,
      });

      const [conversation] = response.points || [];

      if (conversation) {
        // Check if conversation is after last reset
        if (lastResetTimestamp) {
          const conversationTimestamp = conversation.payload?.timestamp as string;
          if (conversationTimestamp && new Date(conversationTimestamp) <= new Date(lastResetTimestamp)) {
            // Conversation is before reset, return null to start fresh
            return null;
          }
        }

        return {
          messages: (conversation.payload.messages as any[]).map(
            msg => {
              if (msg.role === 'system') {
                return new SystemMessage(msg.content);
              } else if (msg.role === 'user' || msg.role === 'human') {
                return new HumanMessage(msg.content);
              } else {
                return new AIMessage(msg.content);
              }
            }
          ),
        };
      }

      return null;
    } catch (error) {
      console.error("Error retrieving conversation from Qdrant:", error);
      return null;
    }
  }

  //Store successful job posting for learning
  private async storeSuccessfulJobPosting(job: any) {
    try {
      const jobStr = `
      Job Title: ${job.title}
      Description: ${job.description}
      Short Description: ${job.shortDescription}
      Skills: ${job.skills?.map(i => i.value).join(', ')}
      Experience: ${job.experienceMin} - ${job.experienceMax}
      Salary: ${job.salaryMonthMin} - ${job.salaryMonthMax} Monthly
      Education: ${job.education}
      Benefits: ${job.benefits?.map(i => i.value).join(', ')}
      Industry: ${job.industry?.label}
      Position: ${job.position?.label}
      `;

      const vector = await this.langchainService.getEmbeddings(jobStr);

      const point = {
        id: job.id,
        vector,
        payload: {
          id: job.id,
          title: job.title,
          companyId: null,
          description: job.description,
          shortDescription: job.shortDescription,
          skills: job.skills,
          experienceMin: job.experienceMin,
          experienceMax: job.experienceMax,
          salaryMonthMin: job.salaryMonthMin,
          salaryMonthMax: job.salaryMonthMax,
          salaryYearMin: job.salaryYearMin,
          salaryYearMax: job.salaryYearMax,
          salaryHourMin: job.salaryHourMin,
          salaryHourMax: job.salaryHourMax,
          education: job.education,
          benefits: job.benefits,
          industry: job.industry,
          position: job.position,
          timestamp: new Date().toISOString(),
          success: true
        }
      };

      await this.qdrantClient.upsert("successful_jobs", {
        wait: true,
        points: [point]
      });
      point.payload.companyId = job.companyId
      await this.qdrantClient.upsert("jobs", {
        wait: true,
        points: [point]
      });


      this.logger.log(`Stored successful job posting for learning: ${job.title}`);
    } catch (error) {
      this.logger.error(`Error storing successful job posting: ${error.message}`, error.stack);
    }
  }

  //Enhance job details generation with learning from successful jobs
  private async enhanceJobDetailsWithLearning(title: string, company: any, similarJobsString: string) {
    try {
      const vector = await this.langchainService.getEmbeddings(title);
      const successfulJobs = await this.qdrantClient.search("successful_jobs", {
        vector,
        limit: 5,
        score_threshold: 0.8
      });

      if (successfulJobs.length === 0) {
        return null;
      }

      // Extract patterns from successful job postings
      const successfulPatterns = await this.llm.invoke(`
      Analyze the following successful job postings and extract common patterns and best practices in 5-6 lines.
      Focus on:
      1. Description structure and content
      2. Salary ranges and benefits
      3. Required skills and experience
      4. Overall tone and presentation
      
      Job Postings:
      ${successfulJobs.map(job => `
        Title: ${job.payload.title}
        Description: ${job.payload.description}
        Skills: ${Array.isArray(job.payload.skills) ? job.payload.skills.join(', ') : ''}
        Experience: ${job.payload.experienceMin}-${job.payload.experienceMax} years
        Salary: $${job.payload.salaryMonthMin}-${job.payload.salaryMonthMax}/month
        Benefits: ${Array.isArray(job.payload.benefits) ? job.payload.benefits.join(', ') : ''}
        Education: ${job.payload.education}
        Industry: ${job.payload.industry}
      `).join('\n\n')}
      
      Return a concise summary of the patterns and best practices which can be helpfull for future job generation.
    `);

      return {
        successfulPatterns: typeof successfulPatterns === 'string' ? successfulPatterns : String(successfulPatterns),
      };
    } catch (error) {
      this.logger.error(`Error enhancing job details with learning: ${error.message}`, error.stack);
      return null;
    }
  }

  //Process user feedback to improve the agent
  private async processUserFeedback({ sessionId, feedback, response }) {
    try {
      const conversationHistory = await this.getConversationHistory(sessionId);
      if (!conversationHistory || !conversationHistory.messages?.length) return;

      const formattedConversation = conversationHistory.messages.map(msg => {
        const msgType = msg._getType();
        const role = msgType === 'human' ? 'User' : 'Assistant';
        const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
        return `${role}: ${content}`;
      }).join('\n');

      const feedbackVector = await this.langchainService.getEmbeddings(feedback);

      await this.qdrantClient.upsert("job_chatbot_user_feedback", {
        wait: true,
        points: [{
          id: uuidv4(),
          vector: feedbackVector,
          payload: {
            sessionId,
            conversation: formattedConversation,
            assistantResponse: response,
            feedback,
            timestamp: new Date().toISOString()
          }
        }]
      });

      this.logger.log(`Stored user feedback for learning: ${feedback.substring(0, 50)}...`);

      // Analyze full interaction for patterns
      await this.analyzeFeedback(feedback, formattedConversation, response);

    } catch (error) {
      this.logger.error(`Error processing user feedback: ${error.message}`, error.stack);
    }
  }

  //Analyze feedback to improve future responses
  private async analyzeFeedback(feedback: string, conversation: string, response: string) {
    try {
      // Perform sentiment analysis with full context
      const sentimentAnalysis = await this.llm.invoke(`
      Based on the following full conversation and the user feedback, determine the overall sentiment.
      Respond with only one word: "positive" or "negative".

      Conversation:
      ${conversation}

      Assistant Response:
      ${response}

      Feedback:
      ${feedback}
    `);

      const sentiment = typeof sentimentAnalysis === 'string'
        ? sentimentAnalysis.trim().toLowerCase()
        : String(sentimentAnalysis).trim().toLowerCase();

      const embeddingInput = `${conversation}\nFeedback: ${feedback}`;
      const vector = await this.langchainService.getEmbeddings(embeddingInput);

      if (sentiment === 'negative') {
        const patternAnalysis = await this.llm.invoke(`
          Analyze the following full conversation and user feedback.
          Identify what made this interaction Unsuccessfull and summarize the pattern that can be avoid in future interaction in 1-2 sentences .
  
          Conversation:
          ${conversation}
  
          Assistant Response:
          ${response}
  
          Feedback:
          ${feedback}
        `);
        await this.qdrantClient.upsert("job_chatbot_problematic_patterns", {
          wait: true,
          points: [{
            id: uuidv4(),
            vector,
            payload: {
              sentiment,
              conversation,
              pattern: typeof patternAnalysis === 'string' ? patternAnalysis : String(patternAnalysis),
              assistantResponse: response,
              feedback,
              timestamp: new Date().toISOString()
            }
          }]
        });
        this.logger.log(`Stored problematic interaction for improvement`);
      }

      if (sentiment === 'positive') {
        const patternAnalysis = await this.llm.invoke(`
        Analyze the following full conversation and user feedback.
        Identify what made this interaction successful and summarize the pattern in 1-2 sentences.

        Conversation:
        ${conversation}

        Assistant Response:
        ${response}

        Feedback:
        ${feedback}
      `);

        await this.qdrantClient.upsert("job_chatbot_successful_patterns", {
          wait: true,
          points: [{
            id: uuidv4(),
            vector,
            payload: {
              sentiment,
              pattern: typeof patternAnalysis === 'string' ? patternAnalysis : String(patternAnalysis),
              conversation,
              assistantResponse: response,
              feedback,
              timestamp: new Date().toISOString()
            }
          }]
        });

        this.logger.log(`Stored successful pattern for future use`);
      }

    } catch (error) {
      this.logger.error(`Error analyzing feedback: ${error.message}`, error.stack);
    }
  }

}
